<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "E8C7BF2D-6664-402E-AC90-6C89E94D046C"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F1B2008E-F55B-494C-94A1-B190FF05D9F8"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Video/Player/MusicPanelView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "763"
            endingLineNumber = "763"
            landmarkName = "startDownload(for:at:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.ExceptionBreakpoint">
         <BreakpointContent
            uuid = "B4F619B3-720E-4990-8278-5A5CA7A13D3F"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            breakpointStackSelectionBehavior = "1"
            scope = "1"
            stopOnStyle = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.SymbolicBreakpoint">
         <BreakpointContent
            uuid = "DAEACA6A-8F0B-4C3F-881C-CE5502E2FDCC"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            symbolName = "UIViewAlertForUnsatisfiableConstraints"
            moduleName = "">
            <Locations>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "FF54B9C7-5319-4009-A708-F0EA9FFAD93F"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Setting/UserInfoEdit/UserInformationEditingPage.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "932"
            endingLineNumber = "932"
            landmarkName = "handleNumEdit(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "84F1EBB2-3611-4E9F-BC3D-5D09042787DA"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Setting/UserInfoEdit/UserInfoEditOccupationViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "35"
            endingLineNumber = "35"
            landmarkName = "backgroundView"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7AF4685F-EC2D-46E1-A3EA-CD5D4BBB3F99"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Setting/UserInfoEdit/YearPickerPopupView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "22"
            endingLineNumber = "22"
            landmarkName = "init(selectedYear:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "72FCBD64-DBE0-401A-9EDB-1E728B81D8CF"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Me/MyCollectionViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "492"
            endingLineNumber = "492"
            landmarkName = "showCleanupConfirmAlert()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F22D70E3-F932-43AE-BD61-D7D92666DD90"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Pods/HXPhotoPicker/Sources/HXPhotoPicker/Editor/Controller/EditorMusicListViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "589"
            endingLineNumber = "589"
            landmarkName = "layoutSubviews()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6C6E25CA-7525-4E03-AE2C-9EF283D9E212"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Account/LoginViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "284"
            endingLineNumber = "284"
            landmarkName = "DesignSpec"
            landmarkType = "14">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "********-AAE6-4586-A795-201258A260B8"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Video/Player/VideoDisplayCenterViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "326"
            endingLineNumber = "326"
            landmarkName = "setupUI()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "9169E114-A284-46FA-9E6F-30D425979807"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Video/Player/VideoDisplayCenterViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "335"
            endingLineNumber = "335"
            landmarkName = "fetchVideoStream()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
