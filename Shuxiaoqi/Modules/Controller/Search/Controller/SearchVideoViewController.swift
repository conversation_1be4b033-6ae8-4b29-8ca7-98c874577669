import UIKit
import SnapKit
import MJRefresh

class SearchVideoViewController: UIViewController, Searchable {
    // MARK: - 筛选条相关
    private let filterContainer = UIView()
    private var filterButtons: [UIButton] = []
    private var selectedFilterIndex: Int = 0
    private var didApplyInitialSelection = false

    // MARK: - 列表
    private var collectionView: UICollectionView!
    private var videoData: [[String: String]] = []
    private var videoItems: [VideoItem] = [] // 保存原始VideoItem数据

    // MARK: - 关键词
    private var currentKeyword: String = ""

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        setupUI()
    }

    // MARK: - UI 构建
    private func setupUI() {
        // 筛选容器
        view.addSubview(filterContainer)
        filterContainer.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        setupFilterBar()

        // 集合视图
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        let itemWidth = (UIScreen.main.bounds.width - 12 * 3) / 2
        layout.itemSize = CGSize(width: itemWidth, height: 364)
        layout.sectionInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)

        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor(hex: "#F5F5F5")
        collectionView.register(DiscoverSearchResultCell.self, forCellWithReuseIdentifier: "DiscoverSearchResultCell")
        collectionView.dataSource = self
        collectionView.delegate = self
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(filterContainer.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }

        // 下拉刷新
        collectionView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(handlePullToRefresh))
    }

    // MARK: - 筛选条
    private func setupFilterBar() {
        let titles = ["综合排序", "最新发布", "最多播放", "时长筛选"]

        // 计算动态宽度
        let screenWidth = UIScreen.main.bounds.width
        let totalMargin: CGFloat = 16 * 2 // 左右边距
        let buttonSpacing: CGFloat = 16 // 按钮间距
        let totalSpacing = buttonSpacing * CGFloat(titles.count - 1)
        let availableWidth = screenWidth - totalMargin - totalSpacing
        let buttonWidth = availableWidth / CGFloat(titles.count)

        var previousButton: UIButton?
        for (index, title) in titles.enumerated() {
            let button = UIButton(type: .custom)
            button.tag = index
            button.setTitle(title, for: .normal)
            button.setTitleColor(UIColor(hex: "#777777"), for: .normal)
            button.titleLabel?.font = .systemFont(ofSize: 14)
            button.addTarget(self, action: #selector(filterButtonTapped(_:)), for: .touchUpInside)
            filterContainer.addSubview(button)
            button.snp.makeConstraints { make in
                make.centerY.equalToSuperview()
                if let prev = previousButton {
                    make.left.equalTo(prev.snp.right).offset(buttonSpacing)
                } else {
                    make.left.equalToSuperview().offset(16)
                }
                make.width.equalTo(buttonWidth)
                make.height.equalTo(29)
            }
            filterButtons.append(button)
            previousButton = button
        }
        // 默认选中第一项
        selectedFilterIndex = 0
    }

    @objc private func filterButtonTapped(_ sender: UIButton) {
        guard sender.tag != selectedFilterIndex else { return }
        selectedFilterIndex = sender.tag
        updateFilterSelectionUI()

        // 根据筛选条件重新请求数据
        print("选择筛选: \(sender.currentTitle ?? ""), sort: \(getSortValue())")
        if !currentKeyword.isEmpty {
            requestVideos(keyword: currentKeyword)
        }
    }

    /// 根据当前选中的筛选按钮获取对应的sort值
    private func getSortValue() -> Int {
        switch selectedFilterIndex {
        case 0: return 1  // 综合排序 - 默认
        case 1: return 1  // 最新发布
        case 2: return 2  // 最多播放
        case 3: return 3  // 时长最长
        default: return 1 // 默认综合
        }
    }

    private func updateFilterSelectionUI() {
        for btn in filterButtons {
            let isSelected = btn.tag == selectedFilterIndex
            if isSelected {
                btn.setTitleColor(.white, for: .normal)
                btn.titleLabel?.font = .boldSystemFont(ofSize: 14)
                btn.backgroundColor = .clear
                btn.layer.cornerRadius = 14.5
                applyGradientToButton(btn)
                filterContainer.bringSubviewToFront(btn)
            } else {
                btn.setTitleColor(UIColor(hex: "#777777"), for: .normal)
                btn.titleLabel?.font = .systemFont(ofSize: 14)
                removeGradientFromButton(btn)
                btn.backgroundColor = UIColor(hex: "#E7E7E7")
                btn.layer.cornerRadius = 14.5
            }
        }
    }

    // MARK: - 渐变层辅助
    private func applyGradientToButton(_ button: UIButton) {
        removeGradientFromButton(button)
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor(hex: "#FF8D36").cgColor,
            UIColor(hex: "#FF5858").cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0.5)
        gradient.endPoint = CGPoint(x: 1, y: 0.5)
        gradient.cornerRadius = 14.5
        gradient.frame = button.bounds
        gradient.name = "btnGradient"
        button.layer.insertSublayer(gradient, at: 0)
    }

    private func removeGradientFromButton(_ button: UIButton) {
        button.layer.sublayers?.removeAll(where: { $0.name == "btnGradient" })
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 首轮应用
        if !didApplyInitialSelection {
            updateFilterSelectionUI()
            didApplyInitialSelection = true
        }
        // 更新渐变尺寸
        for btn in filterButtons {
            btn.layer.sublayers?.forEach { layer in
                if layer.name == "btnGradient" {
                    layer.frame = btn.bounds
                }
            }
        }
    }

    // 当控制器显示时，再次刷新按钮样式，防止首次布局后渐变丢失
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        updateFilterSelectionUI()
    }

    // MARK: - 数据加载
    private func requestVideos(keyword: String) {
        // 根据当前筛选条件获取sort值
        let sortValue = getSortValue()
        print("[SearchVideo] 请求视频数据 - 关键词: \(keyword), sort: \(sortValue)")

        APIManager.shared.searchVideo(keywords: keyword, page: 0, size: 20, sort: sortValue) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.collectionView.mj_header?.endRefreshing()
                switch result {
                case .success(let response):
                    guard let items = response.data?.list else {
                        self.videoData = []
                        self.videoItems = []
                        self.collectionView.reloadData()
                        return
                    }
                    // 保存原始VideoItem数据
                    self.videoItems = items
                    // 转换为显示用的字典数据
                    self.videoData = items.map { item in
                        [
                            "title": item.worksTitle ?? "--",
                            "type": "热门", // 目前接口无类型字段，可自行定义
                            "duration": item.formattedDuration,
                            "author": item.svUserMainVo?.customerName ?? "匿名",
                            "worksCoverImg": item.fullCoverImageURL ?? "",
                            "avatarUrl": item.svUserMainVo?.fullAvatarURL ?? "",
                            "worksType": String(item.worksType ?? 1) // 传递作品类型
                        ]
                    }
                    self.collectionView.reloadData()
                case .failure(let error):
                    print("搜索视频 API 错误：\(error.localizedDescription)")
                }
            }
        }
    }

    @objc private func handlePullToRefresh() {
        guard !currentKeyword.isEmpty else {
            collectionView.mj_header?.endRefreshing(); return
        }
        requestVideos(keyword: currentKeyword)
    }

    // MARK: - 对外搜索接口
    func search(with keyword: String) {
        currentKeyword = keyword
        requestVideos(keyword: keyword)
    }
}

// MARK: - UICollectionViewDataSource & Delegate
extension SearchVideoViewController: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int { videoData.count }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "DiscoverSearchResultCell", for: indexPath) as! DiscoverSearchResultCell
        cell.configure(with: videoData[indexPath.item])
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 防止重复点击
        collectionView.isUserInteractionEnabled = false
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            collectionView.isUserInteractionEnabled = true
        }

        // 确保索引有效且有对应的VideoItem数据
        guard indexPath.item < videoItems.count else {
            print("[SearchVideo] 无效的索引: \(indexPath.item), 总数: \(videoItems.count)")
            return
        }

        let selectedVideoItem = videoItems[indexPath.item]
        print("[SearchVideo] 点击视频: \(selectedVideoItem.worksTitle ?? "未知标题"), ID: \(selectedVideoItem.id ?? 0)")

        // 创建视频播放控制器，传入选中的VideoItem
        let playerVC = VideoDisplayCenterViewController(
            videoItem: selectedVideoItem,
            hideNavBackButton: false,
            showCustomNavBar: true,
            needsTabBarOffset: false
        )
        playerVC.modalPresentationStyle = .fullScreen
        present(playerVC, animated: true)
    }
} 
