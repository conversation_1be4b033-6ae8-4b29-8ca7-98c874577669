//搜索-商家
import UIKit
import SnapKit
import Kingfisher

/// 商家模型
struct MerchantMock {
    let name: String
    let distance: String  // 例如 "100米"
    let time: String      // 例如 "42分钟"
    let tags: [String]    // 例如 ["到店商家", "外卖商家"]
    let feature: String   // 例如 "商品免费领"
    let imageURL: String  // 封面图
}

class SearchMerchantViewController: UIViewController, Searchable {
    // MARK: - UI 组件
    /// 自定义筛选容器
    private let filterContainer = UIView()
    private var filterButtons: [UIButton] = []
    private var selectedFilterIndex: Int = 0

    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.backgroundColor = UIColor(hex: "#F5F5F5")
        tv.separatorStyle = .none
        tv.dataSource = self
        tv.delegate = self
        tv.register(MerchantTableViewCell.self, forCellReuseIdentifier: MerchantTableViewCell.reuseIdentifier)
        return tv
    }()

    // MARK: - 数据
    private var shops: [EcommerceShopItem] = []

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        setupUI()
        fetchShops()
    }

    // MARK: - UI 布局
    private func setupUI() {
        view.addSubview(filterContainer)
        view.addSubview(tableView)

        setupFilterBar()

        filterContainer.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }

        tableView.snp.makeConstraints { make in
            make.top.equalTo(filterContainer.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }

    // MARK: - 网络请求
    // Searchable 协议方法
    func search(with keyword: String) {
        // 触发新的搜索
        fetchShops(keyword: keyword)
    }

    private func fetchShops(keyword: String = "") {
        let request = EcommerceShopSearchRequest(keywords: keyword, page: 0)
        APIManager.shared.searchEcommerceShop(request: request) { [weak self] result in
            switch result {
                case .success(let response):

                DispatchQueue.main.async {
                    self?.shops = response.data.data
                    self?.tableView.reloadData()
                }
            case .failure(let error):
                print("网络错误: \(error)")
            }
        }
    }

    // MARK: - 筛选条
    private func setupFilterBar() {
        let titles = ["综合排序", "最新上线"]
        var previousButton: UIButton?
        for (index, title) in titles.enumerated() {
            let button = UIButton(type: .custom)
            button.setTitle(title, for: .normal)
            button.setTitleColor(UIColor(hex: "#777777"), for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            button.tag = index
            button.addTarget(self, action: #selector(filterButtonTapped(_:)), for: .touchUpInside)
            filterContainer.addSubview(button)

            // constraints
            button.snp.makeConstraints { make in
                make.centerY.equalToSuperview()
                if let prev = previousButton {
                    make.left.equalTo(prev.snp.right).offset(24)
                } else {
                    make.left.equalToSuperview().offset(16)
                }
                make.width.equalTo(84)
                make.height.equalTo(29)
            }

            filterButtons.append(button)
            previousButton = button
        }

        // 记录默认选中索引，等待布局完成后再应用样式
        if let firstButton = filterButtons.first {
            selectedFilterIndex = firstButton.tag
        }
    }

    @objc private func filterButtonTapped(_ sender: UIButton) {
        guard sender.tag != selectedFilterIndex else { return }
        selectedFilterIndex = sender.tag
        updateFilterSelectionUI()
        // TODO: 根据筛选重新排序数据或刷新
        print("选择了筛选: \(sender.tag)")
    }

    private func updateFilterSelectionUI() {
        for btn in filterButtons {
            let isSelected = btn.tag == selectedFilterIndex
            if isSelected {
                btn.setTitleColor(.white, for: .normal)
                btn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 14)
                btn.backgroundColor = .clear // 移除灰色背景
                btn.layer.cornerRadius = 14.5
                applyGradientToButton(btn)
                // 确保选中按钮在最上层，避免被未选中按钮遮挡
                filterContainer.bringSubviewToFront(btn)
            } else {
                btn.setTitleColor(UIColor(hex: "#777777"), for: .normal)
                btn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
                removeGradientFromButton(btn)
                btn.backgroundColor = UIColor(hex: "#E7E7E7")
                btn.layer.cornerRadius = 14.5
            }
        }
    }

    // MARK: - 渐变背景辅助
    private func applyGradientToButton(_ button: UIButton) {
        // 移除旧的
        removeGradientFromButton(button)
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor(hex: "#FF8D36").cgColor,
            UIColor(hex: "#FF5858").cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0.5)
        gradient.endPoint = CGPoint(x: 1, y: 0.5)
        gradient.cornerRadius = 14.5
        gradient.frame = button.bounds
        gradient.name = "btnGradient"
        button.layer.insertSublayer(gradient, at: 0)
    }

    private func removeGradientFromButton(_ button: UIButton) {
        button.layer.sublayers?.removeAll(where: { $0.name == "btnGradient" })
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 仅首次布局后应用一次完整样式
        if !didApplyInitialSelection {
            updateFilterSelectionUI()
            didApplyInitialSelection = true
        }

        // 更新渐变层frame
        for btn in filterButtons {
            btn.layer.sublayers?.forEach { layer in
                if layer.name == "btnGradient" {
                    layer.frame = btn.bounds
                }
            }
        }
    }

    // 标记首轮样式应用
    private var didApplyInitialSelection = false

    // 当控制器显示时，确保筛选按钮样式正确（解决首次切换子控制器后样式缺失问题）
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        updateFilterSelectionUI()
    }
}

// MARK: - UITableViewDataSource & Delegate
extension SearchMerchantViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return shops.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: MerchantTableViewCell.reuseIdentifier, for: indexPath) as? MerchantTableViewCell else {
            return UITableViewCell()
        }
        cell.configure(with: shops[indexPath.row])
        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        // TODO: 进入商家详情
    }
}


//curl --request GET \
//  --url https://test-youshu.gzyoushu.com/video/api/goods/v1/searchGood.do \
//  --header 'Accept: */*' \
//  --header 'Accept-Encoding: gzip, deflate, br' \
//  --header 'Authorization: eyJhbGciOiJIUzUxMiJ9.eyJzZXNzaW9uS2V5IjoieXNfdmlkZW8iLCJzdWIiOiJmZjgwODA4MTk2NzRjYmMwMDE5Njc0ZDE1OWYwMDAwMSIsImlhdCI6MTc1MDUxMDUyMiwiZXhwIjoxNzU0MTEwNTIyfQ.2QhN2no7bx-lUJM26k49hUdOdphW0t3DCNDy9mweCIROcFY1R4UF7C38wIhBbgzm-JNHGgUna9otOuwn-eAnEg' \
//  --header 'Connection: keep-alive' \
//  --header 'Cookie: JSESSIONID=06DFD0540EAF4DA1331A7F290F0ABE93' \
//  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
//  --header 'content-type: multipart/form-data' \
//  --form page=0 \
//  --form searchType=shop \
//  --form size=10 \
//  --form keywords=


//{
//    "status": 1,
//    "errMsg": "",
//    "data": {
//        "data": [
//            {
//                "score": 0,
//                "isFocus": 0,
//                "service": 0,
//                "companyName": "极易净水科技、(上海)有限公司",
//                "description": 0,
//                "logistics": 0,
//                "id": "ff80808192d1a2bb0192d25b8ae10041",
//                "avatar": null,
//                "good": [
//                    {
//                        "min": 1,
//                        "max": 100,
//                        "goodsId": 8539201614332428288,
//                        "goodsImages": "http://test-image.gzyoushu.com/7d4614d400b94967892a45ae3d287349.png",
//                        "name": "按时按时按时",
//                        "id": 339,
//                        "sales": 0
//                    }
//                ],
//                "focusNum": 0
//            },
//            {
//                "score": 4.82,
//                "isFocus": 0,
//                "service": 4.84,
//                "companyName": "树小柒旗舰店",
//                "description": 4.8,
//                "logistics": 4.83,
//                "id": "ys666",
//                "avatar": null,
//                "good": [
//                    {
//                        "min": 1000,
//                        "max": 2000,
//                        "goodsId": 8320985105501257728,
//                        "goodsImages": "http://test-image.gzyoushu.com/dc7213248a034b078ae024ea3e4ff01c.png",
//                        "name": "失效失效商品3.6",
//                        "id": 292,
//                        "sales": 0
//                    },
//                    {
//                        "min": 2,
//                        "max": 30,
//                        "goodsId": 8321004901340413952,
//                        "goodsImages": "http://test-image.gzyoushu.com/b81ac4a2906f4391820a5a27d30bb463.png",
//                        "name": "疯狂学生易购2.0",
//                        "id": 295,
//                        "sales": 0
//                    },
//                    {
//                        "min": null,
//                        "max": null,
//                        "goodsId": 8327069839716253696,
//                        "goodsImages": "http://test-image.gzyoushu.com/6171763dd56f4ae1a21f373b13d73bf5.png",
//                        "name": "易购虚拟-疯狂测试直播【录播课】",
//                        "id": 310,
//                        "sales": 0
//                    }
//                ],
//                "focusNum": 1
//            }
//        ],
//        "totalPage": 1
//    }
//}
