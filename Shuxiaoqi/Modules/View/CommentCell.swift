/*
 一级评论 Cell 需求说明（2024-07-14 / v2.0）
 
 整体结构：评论列表由三种 Cell 组成
 1) CommentCell          —— 一级评论内容（本文件）
 2) ReplyCell            —— 二级评论内容
 3) ExpandRepliesCell    —— 展开 / 收起回复按钮
 CommentCell 不再负责“展开 N 条回复”按钮的展示及点击逻辑。
 
 需求列表：
 1. 仅展示一级评论本身：头像、昵称、作者标签、时间、文字内容、可选图片、点赞 / 不喜欢。
 2. 可选图片：commentImg 字段存在时，在文字下方展示一张 70×70 图片，使用 Kingfisher 异步加载。
 3. 点赞 / 不喜欢：按钮状态由 isLiked / isDisliked 控制，点击通过回调向外层上报。
 4. 作者标签：若评论用户为视频作者（isAuthor == true），在用户名后展示“作者”橙色标签。
 5. @ 功能：若 mentionRanges 不为空，对应文本区段高亮为蓝色。
 6. 不处理二级评论展示与加载；二级评论由 ReplyCell 管理，展开 / 收起按钮由 ExpandRepliesCell 管理。
 7. UI 视觉及尺寸遵循产品设计图，与 1.0 版本保持一致。
*/
import UIKit
import Kingfisher
import SnapKit

class CommentCell: UITableViewCell {
    // MARK: - UI Components
    private let avatarImageView = UIImageView()
    private let usernameLabel = UILabel()
    private let authorTagContainerView = UIView()  // 渐变背景容器
    private let authorTagLabel = UILabel()         // 文字标签
    private let timeLabel = UILabel()
    private let topTagLabel = UILabel()  // 新增置顶标签
    private let contentLabel = UILabel()
    private let commentImageView = UIImageView()
    private let likeButton = UIButton(type: .custom)
    private let dislikeButton = UIButton(type: .custom)
    private let likeCountLabel = UILabel()
    // 折叠占位视图（包含图标+文字）
    private let foldedIconView = UIImageView(image: UIImage(named: "video_dislike_tisp"))
    private let foldedLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        label.text = "该评论已被折叠"
        return label
    }()
    private lazy var foldedStack: UIStackView = {
        let stack = UIStackView(arrangedSubviews: [foldedIconView, foldedLabel])
        stack.axis = .horizontal
        stack.alignment = .center
        stack.spacing = 6
        stack.isHidden = true
        return stack
    }()
    private let regionLabel = UILabel()  // 地区标签，默认隐藏

    // 存储当前评论的@用户信息
    private var currentMentionRanges: [MentionRange] = []

    // 回调
    var likeCallback: (() -> Void)?
    var dislikeCallback: (() -> Void)?
    // 回调：点击头像/用户名
    var avatarTapCallback: (() -> Void)?
    // 回调：点击@用户名
    var mentionTapCallback: ((String) -> Void)?
    // MARK: - 长按手势回调
    var longPressCallback: (() -> Void)?
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        // 添加长按手势
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress))
        contentView.addGestureRecognizer(longPress)
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    
    // MARK: - UI Setup
    private func setupUI() {
        backgroundColor = UIColor(hex: "#F5F5F5")
        selectionStyle = .none
        
        avatarImageView.contentMode = .scaleAspectFill
        avatarImageView.clipsToBounds = true
        avatarImageView.layer.cornerRadius = 20
        avatarImageView.backgroundColor = .lightGray
        contentView.addSubview(avatarImageView)
        // 允许交互并添加点击手势
        avatarImageView.isUserInteractionEnabled = true
        let avatarTap = UITapGestureRecognizer(target: self, action: #selector(handleAvatarTap))
        avatarImageView.addGestureRecognizer(avatarTap)
        avatarImageView.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(16)
            make.size.equalTo(40)
        }
        
        usernameLabel.font = .boldSystemFont(ofSize: 15)
        usernameLabel.textColor = UIColor(hex: "#333333")
        contentView.addSubview(usernameLabel)
        // 用户名标签也支持点击
        usernameLabel.isUserInteractionEnabled = true
        let nameTap = UITapGestureRecognizer(target: self, action: #selector(handleAvatarTap))
        usernameLabel.addGestureRecognizer(nameTap)
        usernameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.top.equalTo(avatarImageView).offset(4)
            make.height.equalTo(16)
        }
        
        // 设置作者标签容器（渐变背景）
        authorTagContainerView.layer.cornerRadius = 4
        authorTagContainerView.layer.masksToBounds = true
        authorTagContainerView.isHidden = true
        contentView.addSubview(authorTagContainerView)
        authorTagContainerView.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel.snp.right).offset(6)
            make.centerY.equalTo(usernameLabel)
            make.width.equalTo(34)
            make.height.equalTo(17)
        }

        // 设置作者标签文字
        authorTagLabel.font = .systemFont(ofSize: 12)
        authorTagLabel.textColor = .white
        authorTagLabel.backgroundColor = .clear
        authorTagLabel.text = "作者"
        authorTagLabel.textAlignment = .center
        authorTagContainerView.addSubview(authorTagLabel)
        authorTagLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 新增置顶标签
        topTagLabel.font = .systemFont(ofSize: 12)
        topTagLabel.textColor = .white
        topTagLabel.backgroundColor = UIColor(hex: "#FF5D1F")
        topTagLabel.layer.cornerRadius = 4
        topTagLabel.layer.masksToBounds = true
        topTagLabel.text = "置顶"
        topTagLabel.textAlignment = .center
        topTagLabel.isHidden = true
        contentView.addSubview(topTagLabel)
        topTagLabel.snp.makeConstraints { make in
            make.left.equalTo(authorTagContainerView.snp.right).offset(6)
            make.centerY.equalTo(usernameLabel)
            make.height.equalTo(18)
        }

        // 移动 timeLabel 到底部
        timeLabel.font = .systemFont(ofSize: 12)
        timeLabel.textColor = UIColor(hex: "#999999")
        contentView.addSubview(timeLabel)
        // timeLabel 约束在后面设置，依赖 commentImageView

        // 点赞 / 不喜欢布局到下方，与 timeLabel 对齐
        dislikeButton.setImage(UIImage(named: "video_dislike"), for: .normal)
        dislikeButton.setImage(UIImage(named: "video_dislike_selected"), for: .selected)
        dislikeButton.addTarget(self, action: #selector(dislikeButtonTapped), for: .touchUpInside)
        contentView.addSubview(dislikeButton)

        likeButton.setImage(UIImage(named: "video_like"), for: .normal)
        likeButton.setImage(UIImage(named: "video_like_selected"), for: .selected)
        likeButton.addTarget(self, action: #selector(likeButtonTapped), for: .touchUpInside)
        contentView.addSubview(likeButton)

        likeCountLabel.font = .systemFont(ofSize: 12)
        likeCountLabel.textColor = UIColor(hex: "#999999")
        likeCountLabel.textAlignment = .left
        contentView.addSubview(likeCountLabel)

        contentLabel.font = .systemFont(ofSize: 14)
        contentLabel.textColor = UIColor(hex: "#333333")
        contentLabel.numberOfLines = 0
        contentLabel.isUserInteractionEnabled = true
        let contentTap = UITapGestureRecognizer(target: self, action: #selector(handleContentTap(_:)))
        contentLabel.addGestureRecognizer(contentTap)
        contentView.addSubview(contentLabel)
        contentLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.top.equalTo(usernameLabel.snp.bottom).offset(8)
            make.right.equalTo(-16) // 右侧贴屏幕
        }

        // 添加折叠占位 stack，与 contentLabel 同位置
        contentView.addSubview(foldedStack)
        foldedStack.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(contentLabel)
            make.right.equalTo(contentLabel)
        }
        // Icon 固定 14×14
        foldedIconView.snp.makeConstraints { make in
            make.width.height.equalTo(14)
        }

        commentImageView.contentMode = .scaleAspectFill
        commentImageView.clipsToBounds = true
        commentImageView.layer.cornerRadius = 8
        contentView.addSubview(commentImageView)
        commentImageView.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(contentLabel.snp.bottom).offset(8)
            make.width.equalTo(70)
            make.height.equalTo(70)
        }

        // timeLabel 约束依赖 commentImageView
        timeLabel.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(commentImageView.snp.bottom).offset(8)
            make.height.greaterThanOrEqualTo(12) // 优化：允许自适应高度，避免约束冲突
            make.bottom.equalTo(-8) // Cell 底部
        }

        // 地区标签（暂时隐藏，等待后端字段）
        regionLabel.font = .systemFont(ofSize: 12)
        regionLabel.textColor = UIColor(hex: "#999999")
        regionLabel.isHidden = true
        contentView.addSubview(regionLabel)
        regionLabel.snp.makeConstraints { make in
            make.left.equalTo(timeLabel.snp.right).offset(5)
            make.centerY.equalTo(timeLabel)
        }

        // 点赞 / 不喜欢 / 计数
        dislikeButton.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.centerY.equalTo(timeLabel)
            make.width.height.equalTo(20)
        }

        likeCountLabel.snp.makeConstraints { make in
            make.right.equalTo(dislikeButton.snp.left).offset(-14)
            make.centerY.equalTo(timeLabel)
        }

        likeButton.snp.makeConstraints { make in
            make.right.equalTo(likeCountLabel.snp.left).offset(-4)
            make.centerY.equalTo(timeLabel)
            make.width.height.equalTo(20)
        }

        // 更新 likeCountLabel 的压缩和优先级，避免被压缩
        likeCountLabel.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        likeCountLabel.setContentCompressionResistancePriority(.defaultHigh, for: .horizontal)

        // 保持用户名的 Hugging 属性
        usernameLabel.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        usernameLabel.setContentCompressionResistancePriority(.defaultHigh, for: .horizontal)
    }

    // MARK: - 辅助方法
    private func setupAuthorLabel() {
        // 确保文字颜色和字体正确设置
        authorTagLabel.textColor = .white
        authorTagLabel.font = .systemFont(ofSize: 12)

        // 应用渐变背景到容器视图
        authorTagContainerView.applyHorizontalGradient(
            colors: [UIColor(hex: "#FF8D36"), UIColor(hex: "#FF5858")]
        )
    }

    // MARK: - 配置方法
    func configure(with comment: CommentModel) {
        // 确保作者标签文字和样式正确设置
        setupAuthorLabel()
        authorTagLabel.text = "作者" // 复用时确保文字存在
        // 头像
        if comment.avatar.hasPrefix("http") {
            avatarImageView.kf.setImage(with: URL(string: comment.avatar), placeholder: UIImage(named: "default_avatar"))
        } else {
            avatarImageView.image = UIImage(named: comment.avatar) ?? UIImage(named: "default_avatar")
        }
        // 用户名
        usernameLabel.text = comment.username
        // 作者标签
        authorTagContainerView.isHidden = !comment.isAuthor
        // 置顶标签
        topTagLabel.isHidden = !comment.isTop
        // @标签
//        if let replyTo = comment.replyTo, !replyTo.isEmpty {
//            atLabel.isHidden = false
//            atLabel.text = "@" + replyTo
//        } else {
//            atLabel.isHidden = true
//        }
        // 时间
        timeLabel.text = comment.timestamp
        // 地区
        if let address = comment.address, !address.isEmpty {
            regionLabel.text = address
            regionLabel.isHidden = false
        } else {
            regionLabel.isHidden = true
        }
        if comment.isDisliked {
            // 折叠显示：保留一行空白文本占位，显示遮罩视图
            contentLabel.isHidden = false
            contentLabel.text = " " // 占位空白，确保 AutoLayout 计算高度
            foldedStack.isHidden = false
            commentImageView.snp.updateConstraints { make in
                make.top.equalTo(contentLabel.snp.bottom)
                make.height.equalTo(0.001)
            }
        } else {
            foldedStack.isHidden = true
            contentLabel.isHidden = false
            // 内容
            let attr = NSMutableAttributedString(string: comment.content)
            for m in comment.mentionRanges {
                if m.range.location + m.range.length <= attr.length {
                    attr.addAttribute(.foregroundColor, value: UIColor(hex: "#2F5B9D"), range: m.range)
                }
            }
            contentLabel.attributedText = attr
            // 保存@用户信息用于点击检测
            currentMentionRanges = comment.mentionRanges
            // 图片
            if let img = comment.imageUrl, !img.isEmpty {
                commentImageView.kf.setImage(with: URL(string: img))
                commentImageView.snp.updateConstraints { make in
                    make.top.equalTo(contentLabel.snp.bottom).offset(8)
                    make.height.equalTo(70)
                }
            } else {
                commentImageView.snp.updateConstraints { make in
                    make.top.equalTo(contentLabel.snp.bottom)
                    make.height.equalTo(0.001)
                }
            }
        }
        // 点赞/不喜欢
       likeButton.isSelected = comment.isLiked
       dislikeButton.isSelected = comment.isDisliked
       likeCountLabel.text = comment.likes > 0 ? "\(comment.likes)" : ""

        //更新布局
        contentView.setNeedsLayout()
        contentView.layoutIfNeeded()
    }
    
    // MARK: - 事件
    @objc private func likeButtonTapped() { likeCallback?() }
    @objc private func dislikeButtonTapped() { dislikeCallback?() }
    @objc private func handleAvatarTap() {
        avatarTapCallback?()
    }
    @objc private func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        if gesture.state == .began {
            longPressCallback?()
        }
    }

    @objc private func handleContentTap(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: contentLabel)

        // 获取点击位置对应的字符索引
        guard let attributedText = contentLabel.attributedText else { return }

        let textStorage = NSTextStorage(attributedString: attributedText)
        let layoutManager = NSLayoutManager()
        let textContainer = NSTextContainer(size: contentLabel.bounds.size)

        textContainer.lineFragmentPadding = 0
        textContainer.maximumNumberOfLines = contentLabel.numberOfLines
        textContainer.lineBreakMode = contentLabel.lineBreakMode

        layoutManager.addTextContainer(textContainer)
        textStorage.addLayoutManager(layoutManager)

        let characterIndex = layoutManager.characterIndex(for: location, in: textContainer, fractionOfDistanceBetweenInsertionPoints: nil)

        // 检查点击位置是否在@用户名范围内
        for mentionRange in currentMentionRanges {
            if NSLocationInRange(characterIndex, mentionRange.range) {
                mentionTapCallback?(mentionRange.id)
                return
            }
        }
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        // 更新渐变背景frame
        authorTagContainerView.updateGradientFrame()
    }
}
