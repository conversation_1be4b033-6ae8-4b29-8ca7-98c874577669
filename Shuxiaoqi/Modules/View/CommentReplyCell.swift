import UIKit
import Kingfisher
import SnapKit

/// 二级评论专用 Cell（Reply）
class CommentReplyCell: UITableViewCell {
    private let avatarImageView = UIImageView()
    private let usernameLabel = UILabel()
    private let authorTagContainerView = UIView()  // 作者标签渐变背景容器
    private let authorTagLabel = UILabel()         // 作者标签文字
    private let atLabel = UILabel()
    private let regionLabel = UILabel() // 地区标签，默认隐藏
    private let timeLabel = UILabel()
    private let contentLabel = UILabel()
    private let commentImageView = UIImageView()
    private let likeButton = UIButton(type: .custom)
    private let dislikeButton = UIButton(type: .custom)
    private let likeCountLabel = UILabel()

    // 存储当前评论的@用户信息
    private var currentMentionRanges: [MentionRange] = []

    // 回调
    var likeCallback: (() -> Void)?
    var dislikeCallback: (() -> Void)?
    // 新增：头像/用户名点击回调
    var avatarTapCallback: (() -> Void)?
    // 回调：点击@用户名
    var mentionTapCallback: ((String) -> Void)?
    // MARK: - 长按手势回调
    var longPressCallback: (() -> Void)?

    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        // 添加长按手势
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress))
        contentView.addGestureRecognizer(longPress)
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

    private func setupUI() {
        backgroundColor = UIColor(hex: "#F5F5F5")
        selectionStyle = .none

        // 头像
        avatarImageView.contentMode = .scaleAspectFill
        avatarImageView.clipsToBounds = true
        avatarImageView.layer.cornerRadius = 16
        avatarImageView.backgroundColor = .lightGray
        contentView.addSubview(avatarImageView)
        // 新增：添加点击手势
        avatarImageView.isUserInteractionEnabled = true
        let avatarTap = UITapGestureRecognizer(target: self, action: #selector(handleAvatarTap))
        avatarImageView.addGestureRecognizer(avatarTap)
        avatarImageView.snp.makeConstraints { make in
            make.left.equalTo(56)
            make.top.equalTo(12)
            make.size.equalTo(32)
        }

        // 用户名
        usernameLabel.font = .systemFont(ofSize: 14)
        usernameLabel.textColor = UIColor(hex: "#333333")
        contentView.addSubview(usernameLabel)
        // 新增：用户名也添加点击手势
        usernameLabel.isUserInteractionEnabled = true
        let nameTap = UITapGestureRecognizer(target: self, action: #selector(handleAvatarTap))
        usernameLabel.addGestureRecognizer(nameTap)
        usernameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.top.equalTo(avatarImageView).offset(2) // 轻微下移，与头像垂直居中对齐
            make.height.equalTo(16) // 明确指定高度
        }

        // 设置作者标签容器（渐变背景）
        authorTagContainerView.layer.cornerRadius = 4
        authorTagContainerView.layer.masksToBounds = true
        authorTagContainerView.isHidden = true
        contentView.addSubview(authorTagContainerView)
        authorTagContainerView.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel.snp.right).offset(6)
            make.centerY.equalTo(usernameLabel)
            make.width.equalTo(34)
            make.height.equalTo(17)
        }

        // 设置作者标签文字
        authorTagLabel.font = .systemFont(ofSize: 12)
        authorTagLabel.textColor = .white
        authorTagLabel.backgroundColor = .clear
        authorTagLabel.text = "作者"
        authorTagLabel.textAlignment = .center
        authorTagContainerView.addSubview(authorTagLabel)
        authorTagLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // @ 标签（用于"回复 <用户名>")
        atLabel.font = .systemFont(ofSize: 12)
        atLabel.textColor = UIColor(hex: "#777777")
        atLabel.isHidden = true
        contentView.addSubview(atLabel)
        atLabel.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(usernameLabel.snp.bottom).offset(3) // 稍微增加间距
            make.height.equalTo(14) // 默认高度
        }

        // 内容 label 需先加入层级，后续图片约束要引用它
        contentView.addSubview(contentLabel)
        contentLabel.font = .systemFont(ofSize: 14)
        contentLabel.textColor = UIColor(hex: "#333333")
        contentLabel.numberOfLines = 0
        contentLabel.isUserInteractionEnabled = true
        let contentTap = UITapGestureRecognizer(target: self, action: #selector(handleContentTap(_:)))
        contentLabel.addGestureRecognizer(contentTap)
        contentLabel.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(atLabel.snp.bottom).offset(5)
            make.right.equalTo(-16)
        }

        // 时间 —— 移至底部
        timeLabel.font = .systemFont(ofSize: 12)
        timeLabel.textColor = UIColor(hex: "#999999")
        contentView.addSubview(timeLabel)
        // 约束稍后设置，依赖 commentImageView

        // 地区标签（隐藏，等待后端字段）
        regionLabel.font = .systemFont(ofSize: 12)
        regionLabel.textColor = UIColor(hex: "#999999")
        regionLabel.isHidden = true
        contentView.addSubview(regionLabel)

        // 图片
        commentImageView.contentMode = .scaleAspectFill
        commentImageView.clipsToBounds = true
        commentImageView.layer.cornerRadius = 8
        contentView.addSubview(commentImageView)
        commentImageView.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(contentLabel.snp.bottom).offset(6)
            make.width.equalTo(60)
            make.height.equalTo(60)
        }

        // 不喜欢按钮（底部，最右侧）
        dislikeButton.setImage(UIImage(named: "video_dislike"), for: .normal)
        dislikeButton.setImage(UIImage(named: "video_dislike_selected"), for: .selected)
        dislikeButton.addTarget(self, action: #selector(dislikeButtonTapped), for: .touchUpInside)
        contentView.addSubview(dislikeButton)

        // 点赞数 & 点赞按钮
        likeCountLabel.font = .systemFont(ofSize: 12)
        likeCountLabel.textColor = UIColor(hex: "#999999")
        likeCountLabel.textAlignment = .left
        contentView.addSubview(likeCountLabel)

        likeButton.setImage(UIImage(named: "video_like"), for: .normal)
        likeButton.setImage(UIImage(named: "video_like_selected"), for: .selected)
        likeButton.addTarget(self, action: #selector(likeButtonTapped), for: .touchUpInside)
        contentView.addSubview(likeButton)

        // 时间 & 点赞等在 commentImageView 之后设置
        // 注：commentImageView 约束已在上方创建
        // bottom row
        timeLabel.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(commentImageView.snp.bottom).offset(8)
            make.height.greaterThanOrEqualTo(12) // 优化：允许自适应高度，避免约束冲突
            make.bottom.equalTo(-8)
        }

        regionLabel.snp.makeConstraints { make in
            make.left.equalTo(timeLabel.snp.right).offset(5)
            make.centerY.equalTo(timeLabel)
        }

        dislikeButton.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.centerY.equalTo(timeLabel)
            make.width.height.equalTo(20)
        }

        likeCountLabel.snp.makeConstraints { make in
            make.right.equalTo(dislikeButton.snp.left).offset(-14)
            make.centerY.equalTo(timeLabel)
        }

        likeButton.snp.makeConstraints { make in
            make.right.equalTo(likeCountLabel.snp.left).offset(-4)
            make.centerY.equalTo(timeLabel)
            make.width.height.equalTo(20)
        }

        // Content hugging / compression priorities
        likeCountLabel.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        likeCountLabel.setContentCompressionResistancePriority(.defaultHigh, for: .horizontal)
        usernameLabel.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        usernameLabel.setContentCompressionResistancePriority(.defaultHigh, for: .horizontal)
    }

    // MARK: - 辅助方法
    private func setupAuthorLabel() {
        // 确保文字颜色和字体正确设置
        authorTagLabel.textColor = .white
        authorTagLabel.font = .systemFont(ofSize: 12)

        // 应用渐变背景到容器视图
        authorTagContainerView.applyHorizontalGradient(
            colors: [UIColor(hex: "#FF8D36"), UIColor(hex: "#FF5858")]
        )
    }

    // MARK: - 配置
    func configure(with comment: CommentModel) {
        // 确保作者标签文字和样式正确设置
        setupAuthorLabel()
        authorTagLabel.text = "作者" // 复用时确保文字存在

        if comment.avatar.hasPrefix("http") {
            avatarImageView.kf.setImage(with: URL(string: comment.avatar), placeholder: UIImage(named: "default_avatar"))
        } else {
            avatarImageView.image = UIImage(named: comment.avatar) ?? UIImage(named: "default_avatar")
        }
        usernameLabel.text = comment.username

        // 作者标签
        authorTagContainerView.isHidden = !comment.isAuthor
        
        // @ 标签（回复目标）
        if let replyTo = comment.replyTo, !replyTo.isEmpty {
            atLabel.isHidden = false
            // 构建富文本："回复" 使用灰色普通字体，用户名使用黑色加粗字体
            let prefixAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 12),
                .foregroundColor: UIColor(hex: "#777777")
            ]
            let usernameAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.boldSystemFont(ofSize: 14),
                .foregroundColor: UIColor(hex: "#333333")
            ]
            let attrText = NSMutableAttributedString(string: "回复 ", attributes: prefixAttributes)
            attrText.append(NSAttributedString(string: replyTo, attributes: usernameAttributes))
            atLabel.attributedText = attrText

            atLabel.snp.updateConstraints { make in
                make.height.equalTo(14)
            }
        } else {
            atLabel.isHidden = true
            atLabel.snp.updateConstraints { make in
                make.height.equalTo(0.01)
            }
        }
        
        // 根据 atLabel 是否显示，重新设置 contentLabel 的约束
        contentLabel.snp.remakeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.right.equalTo(-16)
            if atLabel.isHidden {
                make.top.equalTo(usernameLabel.snp.bottom).offset(5)
            } else {
                make.top.equalTo(atLabel.snp.bottom).offset(5)
            }
        }
        
        // 内容 - 添加@用户名高亮处理，同步一级评论的逻辑
        let attr = NSMutableAttributedString(string: comment.content)
        for m in comment.mentionRanges {
            if m.range.location + m.range.length <= attr.length {
                attr.addAttribute(.foregroundColor, value: UIColor(hex: "#2F5B9D"), range: m.range)
            }
        }
        contentLabel.attributedText = attr
        // 保存@用户信息用于点击检测
        currentMentionRanges = comment.mentionRanges
        
        // 时间
        timeLabel.text = comment.timestamp

        // 地区
        if let address = comment.address, !address.isEmpty {
            regionLabel.text = address
            regionLabel.isHidden = false
        } else {
            regionLabel.isHidden = true
        }
        
        // 图片
        if let img = comment.imageUrl, !img.isEmpty {
            // 有图片
            commentImageView.kf.setImage(with: URL(string: img))
            commentImageView.snp.remakeConstraints { make in
                make.left.equalTo(usernameLabel)
                make.top.equalTo(contentLabel.snp.bottom).offset(8)
                make.width.equalTo(60)
                make.height.equalTo(60)
            }
        } else {
            // 无图片
            commentImageView.snp.remakeConstraints { make in
                make.left.equalTo(usernameLabel)
                make.top.equalTo(contentLabel.snp.bottom)
                make.width.equalTo(0.001)
                make.height.equalTo(0.001)
            }
        }
        
        // 更新时间标签位置，始终在图片下方
        timeLabel.snp.remakeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(commentImageView.snp.bottom).offset(8)
            make.height.greaterThanOrEqualTo(12) // 优化：允许自适应高度，避免约束冲突
            make.bottom.equalTo(-8)
        }
        
        // 更新底部按钮位置，确保它们与时间标签对齐
        regionLabel.snp.remakeConstraints { make in
            make.left.equalTo(timeLabel.snp.right).offset(5)
            make.centerY.equalTo(timeLabel)
        }

        dislikeButton.snp.remakeConstraints { make in
            make.right.equalTo(-16)
            make.centerY.equalTo(timeLabel)
            make.width.height.equalTo(20)
        }

        likeCountLabel.snp.remakeConstraints { make in
            make.right.equalTo(dislikeButton.snp.left).offset(-14)
            make.centerY.equalTo(timeLabel)
        }

        likeButton.snp.remakeConstraints { make in
            make.right.equalTo(likeCountLabel.snp.left).offset(-4)
            make.centerY.equalTo(timeLabel)
            make.width.height.equalTo(20)
        }

        // 点赞/不喜欢
        likeButton.isSelected = comment.isLiked
        dislikeButton.isSelected = comment.isDisliked
        likeCountLabel.text = comment.likes > 0 ? "\(comment.likes)" : ""

        // 更新布局以确保自动高度正确
        contentView.setNeedsLayout()
        contentView.layoutIfNeeded()
    }
    
    // MARK: - 事件
    @objc private func likeButtonTapped() { likeCallback?() }
    @objc private func dislikeButtonTapped() { dislikeCallback?() }
    // 新增：头像/用户名点击事件
    @objc private func handleAvatarTap() { avatarTapCallback?() }
    // MARK: - 长按手势回调
    @objc private func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        if gesture.state == .began {
            longPressCallback?()
        }
    }

    @objc private func handleContentTap(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: contentLabel)

        // 获取点击位置对应的字符索引
        guard let attributedText = contentLabel.attributedText else { return }

        let textStorage = NSTextStorage(attributedString: attributedText)
        let layoutManager = NSLayoutManager()
        let textContainer = NSTextContainer(size: contentLabel.bounds.size)

        textContainer.lineFragmentPadding = 0
        textContainer.maximumNumberOfLines = contentLabel.numberOfLines
        textContainer.lineBreakMode = contentLabel.lineBreakMode

        layoutManager.addTextContainer(textContainer)
        textStorage.addLayoutManager(layoutManager)

        let characterIndex = layoutManager.characterIndex(for: location, in: textContainer, fractionOfDistanceBetweenInsertionPoints: nil)

        // 检查点击位置是否在@用户名范围内
        for mentionRange in currentMentionRanges {
            if NSLocationInRange(characterIndex, mentionRange.range) {
                mentionTapCallback?(mentionRange.id)
                return
            }
        }
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        // 更新渐变背景frame
        authorTagContainerView.updateGradientFrame()
    }
}
