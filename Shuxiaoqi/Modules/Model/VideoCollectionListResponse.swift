//
//  VideoCollectionListResponse.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/5.
//

import SmartCodable

// MARK: - VideoCollectionListResponse
struct VideoCollectionListResponse: SmartCodable {
    var data: DataResponse?
    var errMsg: String?
    var msg: String?
    var status: Int?
    
    // 计算属性：判断请求是否成功
    var isSuccess: Bool {
        return status == 200
    }
    
    // 计算属性：获取用于显示的消息
    var displayMessage: String {
        if let message = msg, !message.isEmpty {
            return message
        }
        if let errorMsg = errMsg, !errorMsg.isEmpty {
            return errorMsg
        }
        return isSuccess ? "成功" : "未知错误"
    }
}

// MARK: - DataResponse
struct DataResponse: SmartCodable {
    var empty: Bool?
    var list: [VideoItem]?
    var pageNum: Int?
    var pageSize: Int?
    var total: Int?
    
    // 计算属性：获取当前页码
    var currentPage: Int {
        return pageNum ?? 1
    }
    
    // 计算属性：获取每页数量
    var itemsPerPage: Int {
        return pageSize ?? 20
    }
    
    // 计算属性：获取总页数
    var totalPages: Int {
        guard let total = total, let pageSize = pageSize, pageSize > 0 else { return 1 }
        return Int(ceil(Double(total) / Double(pageSize)))
    }
    
    // 计算属性：是否有更多数据
    var hasMore: Bool {
        return currentPage < totalPages
    }
}

// MARK: - VideoItem
struct VideoItem: SmartCodable {
    var address: String?
    var allowComment: Int?
    var collectNumber: Int?
    var commentNumber: Int?
    var createBy: String?
//    var createTime: TimeDetail?
    var createTime: String?
    var customerId: String?
    var deleted: Int?
    var duration: Int?
    var followComment: Int?
    var goodIds: String?
    var id: Int?
    var labelIds: String?
    var lat: String?
    var likeNumber: Int?
    var lng: String?
    var shareNumber: Int?
    var state: Int?
    var svUserMainVo: SvUserMainVo?
    var updateBy: String?
    var updateTime: String? = nil
    var videoId: String?
    var watchNumber: Int?
    var worksCoverImg: String?
    var worksDescribe: String?
    var worksTitle: String?
    var worksUrl: [String]?
    var playSign: String?    // 播放签名，由后端直接下发
    var worksType: Int?
    // 新增：是否为当前登录用户的作品
    var isMyWorks: Bool?
    // 新增：后端可能返回的点赞状态字段（备用）
    var isLike: Bool?
    // 新增：后端可能返回的收藏状态字段（备用）
    var isCollect: Bool?
    // 新增：扩展值字段，在点赞列表中用于存储点赞时间
    var extValue: String?
    // 新增：观看时间字段
    var watchDate: String?
    // 新增：查看时间字段
    var lookTime: Int?
    
    // 计算属性：格式化后的时长
    var formattedDuration: String {
        guard let duration = duration else { return "00:00" }
        let minutes = duration / 60
        let seconds = duration % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    // 计算属性：格式化后的点赞数
    var formattedLikeCount: String {
        guard let count = likeNumber else { return "0" }
        if count >= 10000 {
            return String(format: "%.1f万", Double(count) / 10000.0)
        }
        return "\(count)"
    }
    
    // 计算属性：格式化后的评论数
    var formattedCommentCount: String {
        guard let count = commentNumber else { return "0" }
        if count >= 10000 {
            return String(format: "%.1f万", Double(count) / 10000.0)
        }
        return "\(count)"
    }
    
    // 计算属性：格式化后的收藏数
    var formattedCollectCount: String {
        guard let count = collectNumber else { return "0" }
        if count >= 10000 {
            return String(format: "%.1f万", Double(count) / 10000.0)
        }
        return "\(count)"
    }
    
    // 计算属性：格式化后的播放数
    var formattedWatchCount: String {
        guard let count = watchNumber else { return "0" }
        if count >= 10000 {
            return String(format: "%.1f万", Double(count) / 10000.0)
        }
        return "\(count)"
    }
    
    // 计算属性：格式化后的分享数
    var formattedShareCount: String {
        guard let count = shareNumber else { return "0" }
        if count >= 10000 {
            return String(format: "%.1f万", Double(count) / 10000.0)
        }
        return "\(count)"
    }
    
    // 计算属性：完整的封面图片URL
    var fullCoverImageURL: String? {
        guard let coverImg = worksCoverImg else { return nil }
        if coverImg.hasPrefix("http") {
            return coverImg
        }
        return "https://test-youshu.gzyoushu.com/video" + coverImg
    }
    
    // 计算属性：完整的视频URL
    var fullVideoURL: String?
//    {
//        guard let videoUrl = worksUrl else { return nil }
//        if videoUrl.hasPrefix("http") {
//            return videoUrl
//        }
//        return "https://test-youshu.gzyoushu.com/video" + videoUrl
//    }
    
    // 计算属性：是否已删除
    var isDeleted: Bool {
        return deleted == 1
    }

    // 计算属性：是否为自己的作品
    var isMine: Bool {
        return isMyWorks == true
    }
    
    // 计算属性：是否允许评论
    var canComment: Bool {
        return allowComment == 1
    }
    
    // 计算属性：是否已关注
    var isFollowed: Bool {
        return svUserMainVo?.follow == true
    }
    
    // 计算属性：是否已收藏
    var isCollected: Bool {
        // 优先使用 svUserMainVo 中的 collect 字段（最准确）
        if let userInfo = svUserMainVo {
            return userInfo.collect == true
        }
        // 备用：使用直接的 isCollect 字段
        return isCollect == true
    }
    
    // 计算属性：是否已点赞
    var isLiked: Bool {
        // 优先使用 svUserMainVo 中的 like 字段（最准确）
        if let userInfo = svUserMainVo {
            return userInfo.like == true
        }
        // 备用：使用直接的 isLike 字段
        return isLike == true
    }
}

// MARK: - TimeDetail
struct TimeDetail: SmartCodable {
    var date: Int?
    var day: Int?
    var hours: Int?
    var minutes: Int?
    var month: Int?
    var nanos: Int?
    var seconds: Int?
    var time: Int?
    var timezoneOffset: Int?
    var year: Int?
    
    // 计算属性：格式化的时间字符串
    var formattedTime: String {
        guard let year = year,
              let month = month,
              let day = day,
              let hours = hours,
              let minutes = minutes else {
            return "未知时间"
        }
        return String(format: "%04d-%02d-%02d %02d:%02d", year, month, day, hours, minutes)
    }
}

// MARK: - SvUserMainVo
struct SvUserMainVo: SmartCodable {
    var collect: Bool?
    var customerId: String?
    var customerName: String?
    var fansNumber: Int?
    var follow: Bool?
    var like: Bool?
    var state: Int?
    var wxAvator: String?
    var userId: Int? { return Int(customerId ?? "0") }
    
    // 计算属性：格式化后的粉丝数
    var formattedFansCount: String {
        guard let count = fansNumber else { return "0" }
        if count >= 10000 {
            return String(format: "%.1f万", Double(count) / 10000.0)
        }
        return "\(count)"
    }
    
    // 计算属性：完整的头像URL
    var fullAvatarURL: String? {
        guard let avatar = wxAvator else { return nil }
        if avatar.hasPrefix("http") {
            return avatar
        }
        return "https://test-youshu.gzyoushu.com/video" + avatar
    }
}
