/*
 评论数据模型（重构版，2024-07-10）
 统一一级/二级评论结构，便于UI递归展示和业务扩展。
*/

import Foundation
import UIKit

struct CommentModel {
    let id: String                // 评论ID
    let customerId: String        // 用户id
    let avatar: String            // 用户头像URL
    let username: String          // 用户昵称
    let isAuthor: Bool            // 是否为视频作者
    let isTop: Bool = false       // 是否置顶（接口字段 up == 1）
    var content: String           // 评论内容（替换 @ID 为 @昵称 后的显示文本）
    let timestamp: String         // 评论时间
    var likes: Int                // 点赞数
    var dislikes: Int             // 不喜欢数
    var isLiked: Bool             // 当前用户是否点赞
    var isDisliked: Bool          // 当前用户是否点踩
    let imageUrl: String?         // 评论图片URL（commentImg）
    let replyTo: String?          // 被@用户昵称（pcommentUserVo.nickName）
    let address: String?          // 评论地址（address）
    let isHasChild: Bool          // 是否有二级评论
    var childCount: Int           // 二级评论总数
    var replies: [CommentModel]?  // 二级评论数组
    var showExpandReplies: Bool = false   // 是否展示“展开N条回复”按钮，默认 false
    let level: Int            // 评论层级：0=一级，1=二级
    var cellHeight: CGFloat = 0    // 预计算行高（文本高度+图片68）
    // 提及(@)信息
    var mentionRanges: [MentionRange] = []
    // 当前评论是否是登录用户本人发布
    let isMine: Bool
    // 可扩展更多业务字段
}
