//
//  qiNiuContentResponse.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/6/18.
//

import SmartCodable

struct qiNiuContentResponse: SmartCodable {
    var status: Int?
    var errMsg: String?
    var data: [upDataItem]?
    var msg: String?

    var displayMessage: String {
        if let message = msg, !message.isEmpty {
            return message
        }
        if let errorMsg = errMsg, !errorMsg.isEmpty {
            return errorMsg
        }
        return "未知错误"
    }
}

struct upDataItem: SmartCodable {
    var size: String = ""
    var data: [String] = []
    var id: Int = -1
}

//{
//  "status": 200,
//  "errMsg": "",
//  "data": [
//    {
//      "size": "26.57KB   ",
//      "data": [
//        "http://test-image.gzyoushu.com/c207d6870bbf434f912b2c9cd8ef6a48.png"
//      ],
//      "id": 5479
//    }
//  ],
//  "msg": "成功"
//}
