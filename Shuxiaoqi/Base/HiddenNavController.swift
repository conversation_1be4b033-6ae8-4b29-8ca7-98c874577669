import UIKit

// 自定义导航控制器，确保导航栏始终隐藏
class HiddenNavController: UINavigationController {
    
    // 覆盖初始化方法
    override init(rootViewController: UIViewController) {
        super.init(rootViewController: rootViewController)
        // 立即隐藏导航栏
        self.isNavigationBarHidden = true
    }
    
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        // 立即隐藏导航栏
        self.isNavigationBarHidden = true
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        // 立即隐藏导航栏
        self.isNavigationBarHidden = true
    }
    
    // 覆盖加载视图方法
    override func loadView() {
        super.loadView()
        // 确保导航栏隐藏
        setNavigationBarHidden(true, animated: false)
        navigationBar.isHidden = true
    }
    
    // 覆盖视图加载完成方法
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 隐藏导航栏
        setNavigationBarHidden(true, animated: false)
        
        // 设置代理以监听导航事件
        delegate = self
        
        // 打印调试信息
        print("HiddenNavController viewDidLoad")
    }
    
    // 覆盖视图即将出现方法
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 确保导航栏隐藏
        setNavigationBarHidden(true, animated: false)
    }
    
    // 覆盖视图已经出现方法
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        // 确保导航栏隐藏
        setNavigationBarHidden(true, animated: false)
    }
    
    // 覆盖视图即将布局子视图方法
    override func viewWillLayoutSubviews() {
        super.viewWillLayoutSubviews()
        // 确保导航栏隐藏
        setNavigationBarHidden(true, animated: false)
    }
    
    // 覆盖推入视图控制器方法
    override func pushViewController(_ viewController: UIViewController, animated: Bool) {
        // 打印调试信息
        print("HiddenNavController pushViewController: \(type(of: viewController))")
        
        // 如果不是根视图控制器，确保隐藏TabBar
        if viewControllers.count > 0 {
            if let tabBarController = tabBarController as? CustomTabBarController {
                tabBarController.hideTabBar(animated: animated)
                print("HiddenNavController pushViewController: 隐藏TabBar")
            }
        }
        
        super.pushViewController(viewController, animated: animated)
    }
    
    // 覆盖弹出视图控制器方法
    override func popViewController(animated: Bool) -> UIViewController? {
        // 移除 TabBar 管理逻辑，让 BaseViewController 的 updateTabBarVisibility 统一处理
        // 这样可以避免与 BaseViewController 的生命周期方法产生冲突

        print("HiddenNavController popViewController: 让 BaseViewController 统一处理 TabBar 显示")

        // 执行 pop 操作
        return super.popViewController(animated: animated)
    }
    
    // 覆盖弹出到根视图控制器方法
    override func popToRootViewController(animated: Bool) -> [UIViewController]? {
        // 移除 TabBar 管理逻辑，让 BaseViewController 的 updateTabBarVisibility 统一处理
        print("HiddenNavController popToRootViewController: 让 BaseViewController 统一处理 TabBar 显示")

        // 获取结果
        return super.popToRootViewController(animated: animated)
    }
    
    // 覆盖设置视图控制器方法
    override func setViewControllers(_ viewControllers: [UIViewController], animated: Bool) {
        // 确保导航栏隐藏
        setNavigationBarHidden(true, animated: false)
        super.setViewControllers(viewControllers, animated: animated)
    }
    
    // 覆盖设置导航栏隐藏方法
    override func setNavigationBarHidden(_ hidden: Bool, animated: Bool) {
        // 始终隐藏导航栏，忽略参数
        super.setNavigationBarHidden(true, animated: false)
    }
    
    // 添加自定义的弹出手势处理
    func setupCustomPopGesture() {
        // 如果需要支持滑动返回，可以在这里添加自定义的手势识别器
        let edgePanGesture = UIScreenEdgePanGestureRecognizer(target: self, action: #selector(handleCustomPopGesture(_:)))
        edgePanGesture.edges = .left
        view.addGestureRecognizer(edgePanGesture)
    }
    
    @objc private func handleCustomPopGesture(_ gesture: UIScreenEdgePanGestureRecognizer) {
        if gesture.state == .ended {
            if viewControllers.count > 1 {
               _ = popViewController(animated: true)
            }
        }
    }
}

// 添加导航控制器代理扩展
extension HiddenNavController: UINavigationControllerDelegate {
    
    func navigationController(_ navigationController: UINavigationController, willShow viewController: UIViewController, animated: Bool) {
        print("HiddenNavController willShow: \(type(of: viewController))")
        
        // 检查是否是 HomeViewController 或其子控制器
        if viewController is HomeViewController || (viewController.parent is HomeViewController) || (viewController.parent is UIPageViewController && viewController.parent?.parent is HomeViewController) {
            // 如果是 HomeViewController 或其子控制器，不要干扰 TabBar 的显示
            print("HiddenNavController willShow: 跳过 TabBar 显示 (HomeViewController 或其子控制器)")
            return
        }
        
        // 检查是否是 TabBar 根视图控制器
        if let baseVC = viewController as? BaseViewController, baseVC.isTabBarRootViewController {
            // 如果是 TabBar 根视图控制器，显示 TabBar
            if let tabBarController = tabBarController as? CustomTabBarController {
                // 延迟执行，确保在转场完成后显示 TabBar
                DispatchQueue.main.asyncAfter(deadline: .now() + (animated ? 0.3 : 0)) {
                    tabBarController.showTabBar(animated: false)
                    print("HiddenNavController willShow: 显示 TabBar (延迟执行)")
                }
            }
        }
    }
    
    func navigationController(_ navigationController: UINavigationController, didShow viewController: UIViewController, animated: Bool) {
        print("HiddenNavController didShow: \(type(of: viewController))")
        
        // 检查是否是 HomeViewController 或其子控制器
        if viewController is HomeViewController || (viewController.parent is HomeViewController) || (viewController.parent is UIPageViewController && viewController.parent?.parent is HomeViewController) {
            // 如果是 HomeViewController 或其子控制器，不要干扰 TabBar 的显示
            print("HiddenNavController didShow: 跳过 TabBar 显示 (HomeViewController 或其子控制器)")
            return
        }
        
        // 检查是否是 TabBar 根视图控制器
        if let baseVC = viewController as? BaseViewController, baseVC.isTabBarRootViewController {
            // 如果是 TabBar 根视图控制器，显示 TabBar
            if let tabBarController = tabBarController as? CustomTabBarController {
                // 延迟执行，确保在转场完成后显示 TabBar
                DispatchQueue.main.asyncAfter(deadline: .now() + (animated ? 0.3 : 0)) {
                    tabBarController.showTabBar(animated: false)
                    print("HiddenNavController didShow: 显示 TabBar")
                }
            }
        }
    }
} 
