//
//  BaseViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/18.
//

import UIKit
import SnapKit

// MARK: - 基类控制器
class BaseViewController: UIViewController {
    
    // MARK: - 公共属性
    
    /// 导航栏高度
    let navBarHeight: CGFloat = 44
    
    /// 是否显示导航栏
    var showNavBar: Bool = true {
        didSet {
            updateNavigationBarVisibility()
        }
    }
    
    /// 是否使用自定义导航视图替代默认导航栏
    var useCustomNavView: Bool = false {
        didSet {
            setupNavigationBar()
        }
    }
    
    /// 是否完全自定义页面布局（开启后不会自动添加导航栏和contentView）
    var useFullCustomLayout: Bool = false {
        didSet {
            updateFullCustomLayoutMode()
        }
    }
    
    /// 导航栏标题
    var navTitle: String = "" {
        didSet {
            titleLabel.text = navTitle
        }
    }
    
    /// 是否显示返回按钮
    var showBackButton: Bool = true {
        didSet {
            backButton.isHidden = !showBackButton
        }
    }
    
    /// 是否是标签栏的根视图控制器（决定是否显示标签栏）
    var isTabBarRootViewController: Bool = false
    
    /// 导航栏右侧按钮数组
    var navRightItems: [UIButton] = [] {
        didSet {
            updateNavRightItems()
        }
    }
    
    /// 右侧导航按钮标题
    var rightNavTitle: String? {
        didSet {
            updateRightNavButton()
        }
    }
    
    /// 右侧导航按钮点击事件
    var rightNavAction: Selector? {
        didSet {
            updateRightNavButton()
        }
    }
    
    /// 右侧导航按钮文字颜色
    var rightNavButtonTintColor: UIColor = UIColor(hex: "#666666")
    
    // MARK: - UI 组件
    
    /// 内容视图 - 所有子控制器应该将内容添加到这个视图中
    lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .appBackgroundGray
        return view
    }()
    
    /// 默认导航栏视图
    lazy var navBar: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        
        // 添加底部分隔线
        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor(hex: "#EEEEEE")
        view.addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
        
        // 添加返回按钮
        view.addSubview(backButton)
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(15)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        // 添加标题
        view.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.greaterThanOrEqualTo(backButton.snp.right).offset(10)
            make.right.lessThanOrEqualToSuperview().offset(-10)
        }
        
        return view
    }()
    
    /// 自定义导航视图 - 供子类自定义
    lazy var customNavView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        
        // 添加底部分隔线
        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor(hex: "#EEEEEE")
        view.addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
        
        return view
    }()
    
    /// 返回按钮
    lazy var backButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "nav_back"), for: .normal)
        button.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 标题标签
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#333333")
        label.font = UIFont.boldSystemFont(ofSize: 18)
        label.textAlignment = .center
        return label
    }()
    
    // MARK: - 私有属性
    
    /// 右侧导航按钮
    internal var rightNavButton: UIButton?
    
    /// 导航栏右侧按钮容器
    private lazy var navRightButtonsContainer: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 16
        stackView.alignment = .center
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    // MARK: - 生命周期方法
    
    override func loadView() {
        super.loadView()
        hideSystemNavigationBar()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置视图背景色
        view.backgroundColor = .white
        
        // 隐藏系统导航栏
        hideSystemNavigationBar()
        
        // 初始化视图
        if !useFullCustomLayout {
            setupViews()
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 确保系统导航栏隐藏
        hideSystemNavigationBar()
        
        // 处理标签栏的显示和隐藏
        if !shouldSkipTabBarVisibilityUpdate() {
            updateTabBarVisibility()
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 再次确保系统导航栏隐藏
        hideSystemNavigationBar()
        
        // 再次处理标签栏的显示和隐藏
        if !shouldSkipTabBarVisibilityUpdate() {
            updateTabBarVisibility()
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 确保系统导航栏隐藏
        hideSystemNavigationBar()
        
        // 调整内容视图大小
        if !useFullCustomLayout {
            adjustContentViewSize()
        }
    }
    
    // MARK: - 公共方法
    
    /// 添加自定义右侧按钮到导航栏
    func addCustomRightButton(_ button: UIButton) {
        if useCustomNavView {
            customNavView.addSubview(button)
        } else {
            navBar.addSubview(button)
        }
    }
    
    /// 确保导航栏在视图层级的最顶层
    func bringNavBarToFront() {
        if useCustomNavView {
            view.bringSubviewToFront(customNavView)
        } else if showNavBar {
            view.bringSubviewToFront(navBar)
        }
    }
    
    // MARK: - 私有方法
    
    /// 初始化视图布局
    private func setupViews() {
        if useFullCustomLayout {
            return
        }
        
        // 添加内容视图
        view.addSubview(contentView)
        
        // 设置导航栏
        setupNavigationBar()
    }
    
    /// 设置导航栏
    private func setupNavigationBar() {
        if useFullCustomLayout {
            return
        }
        
        // 移除现有导航栏
        navBar.removeFromSuperview()
        customNavView.removeFromSuperview()
        
        // 根据设置决定使用哪种导航栏
        if useCustomNavView {
            // 使用自定义导航视图
            view.addSubview(customNavView)
            
            // 添加右侧按钮容器
            if !navRightItems.isEmpty {
                customNavView.addSubview(navRightButtonsContainer)
                navRightButtonsContainer.snp.makeConstraints { make in
                    make.right.equalToSuperview().offset(-16)
                    make.centerY.equalToSuperview()
                    make.height.equalTo(24)
                }
                updateNavRightItems()
            }
        } else if showNavBar {
            // 使用默认导航栏
            view.addSubview(navBar)
            
            // 添加右侧按钮容器
            if !navRightItems.isEmpty {
                navBar.addSubview(navRightButtonsContainer)
                navRightButtonsContainer.snp.makeConstraints { make in
                    make.right.equalToSuperview().offset(-16)
                    make.centerY.equalToSuperview()
                    make.height.equalTo(24)
                }
                updateNavRightItems()
            }
        }
        
        // 调整内容视图的大小
        adjustContentViewSize()
        
        // 确保导航栏在最上层
        bringNavBarToFront()
    }
    
    /// 隐藏系统导航栏
    private func hideSystemNavigationBar() {
        UIView.performWithoutAnimation {
            navigationController?.setNavigationBarHidden(true, animated: false)
            navigationController?.navigationBar.isHidden = true
            navigationController?.navigationBar.alpha = 0
            
            // 强制布局更新
            navigationController?.view?.layoutIfNeeded()
        }
    }
    
    /// 更新导航栏的可见性
    private func updateNavigationBarVisibility() {
        if useFullCustomLayout {
            return
        }
        
        if showNavBar {
            if useCustomNavView {
                customNavView.isHidden = false
            } else {
                navBar.isHidden = false
            }
        } else {
            navBar.isHidden = true
            customNavView.isHidden = true
        }
        
        // 调整内容视图的大小
        adjustContentViewSize()
    }
    
    /// 更新全自定义布局模式
    private func updateFullCustomLayoutMode() {
        if useFullCustomLayout {
            // 移除标准视图
            contentView.removeFromSuperview()
            navBar.removeFromSuperview()
            customNavView.removeFromSuperview()
        } else {
            // 重新设置标准视图
            setupViews()
        }
    }
    
    /// 调整内容视图的大小
    private func adjustContentViewSize() {
        if useFullCustomLayout {
            return
        }
        
        let tabBarHeight: CGFloat
        
        if isTabBarRootViewController, let tabBarController = self.tabBarController as? CustomTabBarController, !tabBarController.customTabBar.isHidden {
            tabBarHeight = 44 + view.safeAreaInsets.bottom
        } else {
            tabBarHeight = 0
        }
        
        if useCustomNavView && showNavBar {
            // 使用自定义导航视图
            customNavView.frame = CGRect(x: 0, y: view.safeAreaInsets.top, width: view.bounds.width, height: navBarHeight)
            contentView.frame = CGRect(x: 0, y: view.safeAreaInsets.top + navBarHeight, width: view.bounds.width, height: view.bounds.height - view.safeAreaInsets.top - navBarHeight - tabBarHeight)
        } else if showNavBar {
            // 使用默认导航栏
            navBar.frame = CGRect(x: 0, y: view.safeAreaInsets.top, width: view.bounds.width, height: navBarHeight)
            contentView.frame = CGRect(x: 0, y: view.safeAreaInsets.top + navBarHeight, width: view.bounds.width, height: view.bounds.height - view.safeAreaInsets.top - navBarHeight - tabBarHeight)
        } else {
            // 不使用导航栏
            contentView.frame = CGRect(x: 0, y: view.safeAreaInsets.top, width: view.bounds.width, height: view.bounds.height - view.safeAreaInsets.top - tabBarHeight)
        }
    }
    
    /// 判断是否应该跳过TabBar可见性更新
    private func shouldSkipTabBarVisibilityUpdate() -> Bool {
        return self is HomeViewController || (parent is HomeViewController) || (parent is UIPageViewController && parent?.parent is HomeViewController)
    }
    
    /// 更新TabBar的可见性
    func updateTabBarVisibility() {
        guard let tabBarController = self.tabBarController as? CustomTabBarController else {
            // 打印调试信息-需要时打开
//            print("[\(type(of: self))] TabBarController not found or not CustomTabBarController")
            return
        }
        
        // 检查是否是导航控制器的根视图控制器
        let isRootOfNavController = navigationController?.viewControllers.first == self
        
        // 打印调试信息-需要时打开
        print("[\(type(of: self))] TabBar可见性更新: isTabBarRootViewController=\(isTabBarRootViewController), isRootOfNavController=\(isRootOfNavController)")
        
        if isTabBarRootViewController && isRootOfNavController {
            // 如果是主要标签页面且是导航控制器的根视图，显示TabBar
            print("[\(type(of: self))] 显示TabBar")
            
            // 检查当前是否已经显示，避免重复操作导致闪动
            if tabBarController.customTabBar.isHidden {
                tabBarController.showTabBar(animated: false)
            } else {
                // 如果已经显示，只需确保它在最上层
                tabBarController.view.bringSubviewToFront(tabBarController.customTabBar)
            }
        } else {
            // 如果不是主要标签页面或不是导航控制器的根视图，隐藏TabBar
            print("[\(type(of: self))] 隐藏TabBar")
            
            // 检查当前是否已经隐藏，避免重复操作导致闪动
            if !tabBarController.customTabBar.isHidden {
                tabBarController.hideTabBar(animated: false)
            }
        }
    }
    
    /// 更新导航栏右侧按钮
    private func updateNavRightItems() {
        if useFullCustomLayout {
            return
        }
        
        // 清除现有按钮
        navRightButtonsContainer.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // 添加新按钮
        for button in navRightItems {
            navRightButtonsContainer.addArrangedSubview(button)
            button.snp.makeConstraints { make in
                make.size.equalTo(24)
            }
        }
        
        // 确保按钮容器已添加到正确的导航栏
        if useCustomNavView {
            if !customNavView.subviews.contains(navRightButtonsContainer) {
                customNavView.addSubview(navRightButtonsContainer)
                navRightButtonsContainer.snp.makeConstraints { make in
                    make.right.equalToSuperview().offset(-16)
                    make.centerY.equalToSuperview()
                    make.height.equalTo(24)
                }
            }
        } else if showNavBar {
            if !navBar.subviews.contains(navRightButtonsContainer) {
                navBar.addSubview(navRightButtonsContainer)
                navRightButtonsContainer.snp.makeConstraints { make in
                    make.right.equalToSuperview().offset(-16)
                    make.centerY.equalToSuperview()
                    make.height.equalTo(24)
                }
            }
        }
    }
    
    /// 更新右侧导航按钮
    private func updateRightNavButton() {
        if useFullCustomLayout {
            return
        }
        
        // 移除旧按钮
        rightNavButton?.removeFromSuperview()
        rightNavButton = nil
        
        if let title = rightNavTitle, let action = rightNavAction {
            // 创建右侧导航按钮
            let button = UIButton(type: .custom)
            button.setTitle(title, for: .normal)
            button.setTitleColor(rightNavButtonTintColor, for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            button.addTarget(self, action: action, for: .touchUpInside)
            button.contentHorizontalAlignment = .right
            button.tag = 1001
            
            // 保存按钮引用
            rightNavButton = button
            
            // 添加到对应的导航栏
            if useCustomNavView {
                customNavView.addSubview(button)
            } else if showNavBar {
                navBar.addSubview(button)
            }
            
            // 设置按钮位置
            button.snp.makeConstraints { make in
                make.right.equalToSuperview().offset(-12)
                make.centerY.equalToSuperview()
                make.width.equalTo(70)
                make.height.equalTo(40)
            }
        }
    }
    
    // MARK: - 事件响应方法
    
    /// 返回按钮点击事件
    @objc func backButtonTapped() {
        // 尝试返回上一页
        if let navigationController = navigationController, navigationController.viewControllers.count > 1 {
            navigationController.popViewController(animated: true)
        } else {
            // 尝试关闭当前控制器
            dismiss(animated: true, completion: nil)
        }
    }
}
